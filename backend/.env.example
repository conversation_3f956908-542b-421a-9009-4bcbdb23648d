# Doc2Dev Configuration Example
# Copy this file to .env and configure your settings

# =============================================================================
# GitHub OAuth Configuration
# =============================================================================
# GitHub OAuth App credentials (create at https://github.com/settings/applications/new)
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# JWT Secret Key for authentication tokens (change in production)
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production

# =============================================================================
# CORS Configuration
# =============================================================================
# Comma-separated list of allowed origins for CORS
# For Railway deployment, add your frontend domain here
# Example: https://your-frontend.railway.app,https://your-custom-domain.com
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# =============================================================================
# Metadata Database Configuration
# =============================================================================
# Specify database type: mysql or postgresql
METADATA_DB_CONFIG_TYPE=mysql

# MySQL Configuration (when METADATA_DB_CONFIG_TYPE=mysql)
METADATA_DB_MYSQL_HOST=your_mysql_host
METADATA_DB_MYSQL_PORT=3306
METADATA_DB_MYSQL_USER=your_mysql_user
METADATA_DB_MYSQL_PASSWORD=your_mysql_password
METADATA_DB_MYSQL_DATABASE=doc2dev

# PostgreSQL Configuration (when METADATA_DB_CONFIG_TYPE=postgresql)
# METADATA_DB_POSTGRESQL_HOST=your_postgresql_host
# METADATA_DB_POSTGRESQL_PORT=5432
# METADATA_DB_POSTGRESQL_USER=your_postgresql_user
# METADATA_DB_POSTGRESQL_PASSWORD=your_postgresql_password
# METADATA_DB_POSTGRESQL_DATABASE=doc2dev

# =============================================================================
# Vector Store Configuration
# =============================================================================
# Specify vector store type: oceanbase
VECTOR_STORE_CONFIG_TYPE=oceanbase

# OceanBase Vector Store Configuration
VECTOR_STORE_OCEANBASE_HOST=your_oceanbase_host
VECTOR_STORE_OCEANBASE_PORT=3306
VECTOR_STORE_OCEANBASE_USER=your_oceanbase_user
VECTOR_STORE_OCEANBASE_PASSWORD=your_oceanbase_password
VECTOR_STORE_OCEANBASE_DB_NAME=doc2dev

# =============================================================================
# Embedding Configuration
# =============================================================================
# Specify embedding provider: openai, dashscope, huggingface, ollama
EMBEDDING_CONFIG_TYPE=dashscope

# DashScope Configuration (when EMBEDDING_CONFIG_TYPE=dashscope)
EMBEDDING_DASHSCOPE_API_KEY=your_dashscope_api_key
EMBEDDING_DASHSCOPE_MODEL=text-embedding-v3

# OpenAI Embedding Configuration (when EMBEDDING_CONFIG_TYPE=openai)
# EMBEDDING_OPENAI_API_KEY=your_openai_api_key
# EMBEDDING_OPENAI_MODEL=text-embedding-ada-002

# =============================================================================
# LLM Configuration
# =============================================================================
# Specify which LLM provider to use: openai, anthropic, huggingface, ollama, azure_openai
LLM_CONFIG_TYPE=openai

# OpenAI Configuration (when LLM_CONFIG_TYPE=openai)
LLM_OPENAI_API_KEY=your_openai_api_key
LLM_OPENAI_API_BASE=https://openrouter.ai/api/v1
LLM_OPENAI_MODEL=openai/gpt-4.1

# Alternative OpenAI Configuration
# LLM_OPENAI_API_BASE=https://api.openai.com/v1
# LLM_OPENAI_MODEL=gpt-4o

# Anthropic Configuration (when LLM_CONFIG_TYPE=anthropic)
# LLM_ANTHROPIC_API_KEY=your_anthropic_api_key
# LLM_ANTHROPIC_MODEL=claude-3-opus-20240229

# HuggingFace Configuration (when LLM_CONFIG_TYPE=huggingface)
# LLM_HUGGINGFACE_API_KEY=your_huggingface_token
# LLM_HUGGINGFACE_MODEL=microsoft/DialoGPT-medium

# Ollama Configuration (when LLM_CONFIG_TYPE=ollama)
# LLM_OLLAMA_BASE_URL=http://localhost:11434
# LLM_OLLAMA_MODEL=llama2

# =============================================================================
# Application Configuration
# =============================================================================
# API Base URL for the application
API_BASE_URL=http://localhost:8000

# GitHub Token for repository access
GITHUB_TOKEN=your_github_token_here

# Azure OpenAI Configuration (when LLM_CONFIG_TYPE=azure_openai)
# LLM_AZURE_OPENAI_API_KEY=your_azure_openai_key_here
# LLM_AZURE_OPENAI_AZURE_ENDPOINT=https://your-resource.openai.azure.com/
# LLM_AZURE_OPENAI_API_VERSION=2024-02-15-preview
# LLM_AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment_name
# LLM_AZURE_OPENAI_TEMPERATURE=0.3
# LLM_AZURE_OPENAI_MAX_TOKENS=2000

# =============================================================================
# Vector Store Configuration
# =============================================================================
VECTOR_STORE_TYPE=oceanbase

# OceanBase Vector Configuration
VECTOR_STORE_OCEANBASE_HOST=localhost
VECTOR_STORE_OCEANBASE_PORT=2881
VECTOR_STORE_OCEANBASE_USER=root@test
VECTOR_STORE_OCEANBASE_PASSWORD=your_password
VECTOR_STORE_OCEANBASE_DATABASE=test
VECTOR_STORE_OCEANBASE_TABLE_NAME=your_table_name

# =============================================================================
# Embedding Configuration
# =============================================================================
EMBEDDING_TYPE=dashscope

# DashScope Embedding Configuration
EMBEDDING_DASHSCOPE_API_KEY=your_dashscope_api_key_here
EMBEDDING_DASHSCOPE_MODEL=text-embedding-v1

# =============================================================================
# Metadata Database Configuration
# =============================================================================
METADATA_DB_TYPE=mysql

# MySQL Metadata Database Configuration
METADATA_DB_MYSQL_HOST=localhost
METADATA_DB_MYSQL_PORT=3306
METADATA_DB_MYSQL_USER=root
METADATA_DB_MYSQL_PASSWORD=your_password
METADATA_DB_MYSQL_DATABASE=doc2dev

# =============================================================================
# Application Configuration
# =============================================================================

# Application Settings
APP_NAME=Doc2Dev
DEBUG=false
LOG_LEVEL=INFO

# API Configuration
API_BASE_URL=http://localhost:8000
